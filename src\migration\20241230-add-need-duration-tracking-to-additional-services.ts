import { QueryInterface, DataTypes } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface) => {
    // 添加是否需要统计时长字段
    await queryInterface.addColumn(
      'additional_services',
      'needDurationTracking',
      {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: '是否需要统计时长',
      }
    );
  },

  down: async (queryInterface: QueryInterface) => {
    // 删除字段
    await queryInterface.removeColumn(
      'additional_services',
      'needDurationTracking'
    );
  },
};
