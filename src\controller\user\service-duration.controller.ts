import { Controller, Get, Param } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Inject } from '@midwayjs/core';

import { ServiceDurationRecord } from '../../entity/service-duration-record.entity';
import { Order } from '../../entity/order.entity';
import { OrderDetail } from '../../entity/order-detail.entity';
import { Service } from '../../entity/service.entity';
import { AdditionalService } from '../../entity/additional-service.entity';
import { Employee } from '../../entity/employee.entity';
import { CustomError } from '../../error/custom.error';
import { Op } from 'sequelize';
import { OrderDurationCalculatorService } from '../../service/order-duration-calculator.service';

@Controller('/user/service-duration')
export class UserServiceDurationController {
  @Inject()
  ctx: Context;

  @Inject()
  orderDurationCalculatorService: OrderDurationCalculatorService;

  @Get('/records/:orderId')
  async getOrderServiceRecords(@Param('orderId') orderId: number) {
    const customerId = this.ctx.state.user.id;

    // 验证订单是否属于当前用户
    const order = await Order.findByPk(orderId, {
      attributes: ['id', 'customerId'],
    });

    if (!order) {
      throw new CustomError('订单不存在');
    }

    if (order.customerId !== customerId) {
      throw new CustomError('无权限查看此订单');
    }

    // 查询服务时长记录
    const records = await ServiceDurationRecord.findAll({
      where: { orderId },
      include: [
        {
          model: OrderDetail,
          attributes: ['id', 'serviceName', 'petName'],
        },
        {
          model: Employee,
          attributes: ['id', 'name'],
        },
        {
          model: Service,
          attributes: ['id', 'serviceName'],
        },
        {
          model: AdditionalService,
          attributes: ['id', 'name'],
        },
      ],
      order: [['startTime', 'ASC']],
    });

    // 按服务类型分组
    const mainServices = records.filter(
      record => record.recordType === 'main_service'
    );
    const additionalServices = records.filter(
      record => record.recordType === 'additional_service'
    );

    return {
      orderId,
      mainServices,
      additionalServices,
      totalRecords: records.length,
      completedRecords: records.filter(record => record.endTime).length,
      totalDuration: records
        .filter(record => record.duration)
        .reduce((sum, record) => sum + (record.duration || 0), 0),
    };
  }

  @Get('/summary/:orderId')
  async getOrderServiceSummary(@Param('orderId') orderId: number) {
    const customerId = this.ctx.state.user.id;

    // 验证订单是否属于当前用户
    const order = await Order.findByPk(orderId, {
      attributes: ['id', 'customerId', 'sn', 'status'],
    });

    if (!order) {
      throw new CustomError('订单不存在');
    }

    if (order.customerId !== customerId) {
      throw new CustomError('无权限查看此订单');
    }

    // 查询已完成的服务时长记录
    const records = await ServiceDurationRecord.findAll({
      where: {
        orderId,
        duration: { [Op.not]: null },
      },
      include: [
        {
          model: Service,
          attributes: ['id', 'serviceName'],
        },
        {
          model: AdditionalService,
          attributes: ['id', 'name'],
        },
        {
          model: Employee,
          attributes: ['id', 'name'],
        },
      ],
    });

    // 按服务分组统计
    const serviceGroups = records.reduce((acc, record) => {
      const key =
        record.recordType === 'main_service'
          ? `main_${record.serviceId}`
          : `additional_${record.additionalServiceId}`;

      if (!acc[key]) {
        acc[key] = {
          type: record.recordType,
          serviceId: record.serviceId || record.additionalServiceId,
          serviceName:
            record.recordType === 'main_service'
              ? record.service?.serviceName || record.serviceName
              : record.additionalService?.name || record.additionalServiceName,
          employeeName: record.employee?.name,
          records: [],
          totalDuration: 0,
        };
      }

      acc[key].records.push({
        id: record.id,
        startTime: record.startTime,
        endTime: record.endTime,
        duration: record.duration,
        remark: record.remark,
      });
      acc[key].totalDuration += record.duration || 0;

      return acc;
    }, {});

    const serviceList = Object.values(serviceGroups);
    const totalDuration = serviceList.reduce(
      (sum, service: any) => sum + service.totalDuration,
      0
    );

    return {
      orderId,
      orderSn: order.sn,
      orderStatus: order.status,
      services: serviceList,
      summary: {
        totalServices: serviceList.length,
        totalDuration,
        mainServices: serviceList.filter((s: any) => s.type === 'main_service')
          .length,
        additionalServices: serviceList.filter(
          (s: any) => s.type === 'additional_service'
        ).length,
      },
    };
  }

  @Get('/duration-statistics/:orderId')
  async getOrderDurationStatistics(@Param('orderId') orderId: number) {
    const customerId = this.ctx.state.user.id;

    // 验证订单是否属于当前用户
    const order = await Order.findByPk(orderId, {
      attributes: ['id', 'customerId'],
    });

    if (!order) {
      throw new CustomError('订单不存在');
    }

    if (order.customerId !== customerId) {
      throw new CustomError('无权限查看此订单');
    }

    return await this.orderDurationCalculatorService.getOrderDurationStatistics(orderId);
  }
}
