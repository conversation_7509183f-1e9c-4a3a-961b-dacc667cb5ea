import { QueryInterface, DataTypes } from 'sequelize';

export = {
  up: async (queryInterface: QueryInterface) => {
    await queryInterface.createTable('service_duration_records', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '记录ID',
      },
      orderId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '关联订单ID',
        references: {
          model: 'orders',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      orderDetailId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '关联订单详情ID',
        references: {
          model: 'order_details',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      additionalServiceOrderId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '关联追加服务订单ID',
        references: {
          model: 'additional_service_orders',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      employeeId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '关联员工ID',
        references: {
          model: 'employees',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      recordType: {
        type: DataTypes.ENUM('main_service', 'additional_service'),
        allowNull: false,
        comment: '记录类型',
      },
      serviceId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '关联服务ID（主服务时使用）',
        references: {
          model: 'services',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      serviceName: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '服务名称（冗余字段）',
      },
      additionalServiceId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '关联增项服务ID（增项服务时使用）',
        references: {
          model: 'additional_services',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      additionalServiceName: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: '增项服务名称（冗余字段）',
      },
      startTime: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: '服务开始时间',
      },
      endTime: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: '服务结束时间',
      },
      duration: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '服务时长（分钟）',
      },
      remark: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '备注',
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间',
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '更新时间',
      },
    });

    // 添加索引
    await queryInterface.addIndex('service_duration_records', ['orderId']);
    await queryInterface.addIndex('service_duration_records', ['employeeId']);
    await queryInterface.addIndex('service_duration_records', ['orderDetailId']);
    await queryInterface.addIndex('service_duration_records', ['additionalServiceOrderId']);
    await queryInterface.addIndex('service_duration_records', ['serviceId']);
    await queryInterface.addIndex('service_duration_records', ['additionalServiceId']);
    await queryInterface.addIndex('service_duration_records', ['recordType']);
    await queryInterface.addIndex('service_duration_records', ['startTime']);
    await queryInterface.addIndex('service_duration_records', ['endTime']);
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.dropTable('service_duration_records');
  },
};
