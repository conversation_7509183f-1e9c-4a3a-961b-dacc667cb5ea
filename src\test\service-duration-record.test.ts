import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import { Application } from '@midwayjs/koa';

describe('test/service-duration-record.test.ts', () => {
  let app: Application;

  beforeAll(async () => {
    try {
      app = await createApp<Framework>();
    } catch (err) {
      console.error('setup', err);
    }
  });

  afterAll(async () => {
    await close(app);
  });

  it('should POST /employee/service-duration/start', async () => {
    // 模拟员工登录状态
    const mockEmployeeId = 1;
    
    const result = await createHttpRequest(app)
      .post('/employee/service-duration/start')
      .set('Authorization', 'Bearer mock-token') // 模拟token
      .send({
        orderId: 1,
        orderDetailId: 1,
        recordType: 'main_service',
        serviceId: 1,
        remark: '开始测试服务'
      });

    // 注意：这个测试需要数据库中有相应的测试数据
    // 在实际测试中，你可能需要先创建测试订单和服务数据
    console.log('Start service result:', result.body);
  });

  it('should GET /employee/service-duration/current', async () => {
    const result = await createHttpRequest(app)
      .get('/employee/service-duration/current')
      .set('Authorization', 'Bearer mock-token');

    console.log('Current services result:', result.body);
  });

  it('should GET /user/service-duration/records/1', async () => {
    const result = await createHttpRequest(app)
      .get('/user/service-duration/records/1')
      .set('Authorization', 'Bearer mock-token');

    console.log('User service records result:', result.body);
  });

  it('should GET /admin/service-duration-statistics/service-statistics', async () => {
    const result = await createHttpRequest(app)
      .get('/admin/service-duration-statistics/service-statistics')
      .set('Authorization', 'Bearer mock-token');

    console.log('Service statistics result:', result.body);
  });
});
