import { Controller, Get, Post, Query, Body, Inject } from '@midwayjs/core';
import { Vehicle } from '../../entity';
import { VehicleService } from '../../service/vehicle.service';
import { EmployeeService } from '../../service/employee.service';
import { Context } from '@midwayjs/koa';

@Controller('/openapi/location')
export class LocationController {
  @Inject()
  ctx: Context;

  @Inject()
  vehicleService: VehicleService;

  @Inject()
  employeeService: EmployeeService;

  private readonly EARTH_RADIUS = 6371; // 将地球半径定义为常量

  private deg2rad(deg: number) {
    return deg * (Math.PI / 180);
  }

  /**
   * 验证经纬度是否有效
   */
  private isValidCoordinate(lat: number, lng: number): boolean {
    return (
      !isNaN(lat) &&
      !isNaN(lng) &&
      lat >= -90 &&
      lat <= 90 &&
      lng >= -180 &&
      lng <= 180
    );
  }

  /**
   * 计算两个经纬度之间的距离
   * @param lat1 纬度1
   * @param lng1 经度1
   * @param lat2 纬度2
   * @param lng2 经度2
   * @returns 距离（单位：米）
   */
  @Get('/calculateDistance', { summary: '计算两个经纬度之间的距离' })
  calculateDistance(
    @Query('lat1') lat1: number,
    @Query('lng1') lng1: number,
    @Query('lat2') lat2: number,
    @Query('lng2') lng2: number
  ) {
    // 参数验证
    if (
      !this.isValidCoordinate(lat1, lng1) ||
      !this.isValidCoordinate(lat2, lng2)
    ) {
      throw new Error('无效的经纬度坐标');
    }

    const dLat = this.deg2rad(lat2 - lat1);
    const dLng = this.deg2rad(lng2 - lng1);

    const radLat1 = this.deg2rad(lat1);
    const radLat2 = this.deg2rad(lat2);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(radLat1) *
        Math.cos(radLat2) *
        Math.sin(dLng / 2) *
        Math.sin(dLng / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distanceKm = this.EARTH_RADIUS * c;

    return Math.round(distanceKm * 1000); // 转换为米并四舍五入
  }

  @Get('/findNearbyVehicles', { summary: '查找附近车辆' })
  async findNearbyVehicles(
    @Query('lat') lat: number,
    @Query('lng') lng: number,
    @Query('radius') radius: number
  ) {
    // 参数验证
    if (!this.isValidCoordinate(lat, lng)) {
      // throw new Error('无效的经纬度坐标');
      console.error('无效的经纬度坐标');
      return [];
    }
    // 假设这是一个示例，实际应用中应该从数据库或其他数据源获取车辆数据
    const vehicles = await Vehicle.findAll({
      where: {
        status: '空闲',
      },
    });
    // 计算每个车辆与给定经纬度之间的距离
    const nearbyVehicles = vehicles.map(vehicle => {
      const distance = this.calculateDistance(
        lat,
        lng,
        vehicle.latitude,
        vehicle.longitude
      );
      return { ...vehicle.toJSON(), distance };
    });
    console.log(nearbyVehicles);
    // 返回指定范围内（默认10千米）的车辆，若数量大于10个则取前10个
    nearbyVehicles.sort((a, b) => a.distance - b.distance);
    return nearbyVehicles
      .slice(0, 10)
      .filter(vehicle => vehicle.distance <= (radius || 10000));
  }

  @Post('/updateEmployeeLocation', { summary: '员工端上报当前位置' })
  async updateEmployeeLocation(
    @Body()
    body: {
      employeeId: number;
      latitude: number;
      longitude: number;
      address?: string;
    }
  ) {
    const { employeeId, latitude, longitude, address } = body;

    try {
      // 参数验证
      if (!employeeId) {
        this.ctx.logger.warn('员工位置上报失败：员工ID不能为空', { body });
        return { success: false, message: '参数错误' };
      }

      if (!this.isValidCoordinate(latitude, longitude)) {
        this.ctx.logger.warn('员工位置上报失败：无效的经纬度坐标', {
          employeeId,
          latitude,
          longitude,
        });
        return { success: false, message: '坐标无效' };
      }

      // 验证员工是否存在
      const employee = await this.employeeService.findById(employeeId);
      if (!employee) {
        this.ctx.logger.warn('员工位置上报失败：员工不存在', { employeeId });
        return { success: false, message: '员工不存在' };
      }

      // 获取员工关联的车辆
      const vehicle = await this.employeeService.getVehicle(employeeId);
      if (!vehicle) {
        this.ctx.logger.warn('员工位置上报失败：员工未绑定车辆', {
          employeeId,
          employeeName: employee.name,
        });
        return { success: false, message: '未绑定车辆' };
      }

      // 更新车辆位置
      await this.vehicleService.updateLocation(vehicle.id, latitude, longitude);

      this.ctx.logger.info('员工位置上报成功', {
        employeeId,
        employeeName: employee.name,
        vehicleId: vehicle.id,
        plateNumber: vehicle.plateNumber,
        latitude,
        longitude,
        address,
      });

      return {
        success: true,
        employeeId,
        vehicleId: vehicle.id,
        plateNumber: vehicle.plateNumber,
        latitude,
        longitude,
        address,
        updateTime: new Date().toISOString(),
        message: '位置更新成功',
      };
    } catch (error) {
      this.ctx.logger.error('员工位置上报异常', {
        employeeId,
        latitude,
        longitude,
        address,
        error: error.message,
        stack: error.stack,
      });

      return {
        success: false,
        message: '系统异常',
      };
    }
  }
}
