import { Controller, Get, Query } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Inject } from '@midwayjs/core';
import { ApiTags, ApiOperation, ApiQuery } from '@midwayjs/swagger';
import { ServiceDurationRecord } from '../../entity/service-duration-record.entity';
import { Order } from '../../entity/order.entity';
import { OrderDetail } from '../../entity/order-detail.entity';
import { Service } from '../../entity/service.entity';
import { AdditionalService } from '../../entity/additional-service.entity';
import { Employee } from '../../entity/employee.entity';
import { Customer } from '../../entity/customer.entity';
import { Op } from 'sequelize';

@ApiTags('管理端-服务时长统计')
@Controller('/admin/service-duration-statistics')
export class AdminServiceDurationStatisticsController {
  @Inject()
  ctx: Context;

  @Get('/records')
  @ApiOperation({ summary: '查询服务时长记录' })
  @ApiQuery({ name: 'orderId', description: '订单ID', required: false })
  @ApiQuery({ name: 'employeeId', description: '员工ID', required: false })
  @ApiQuery({ name: 'serviceId', description: '服务ID', required: false })
  @ApiQuery({ name: 'additionalServiceId', description: '增项服务ID', required: false })
  @ApiQuery({ name: 'startDate', description: '开始日期', required: false })
  @ApiQuery({ name: 'endDate', description: '结束日期', required: false })
  @ApiQuery({ name: 'page', description: '页码', required: false })
  @ApiQuery({ name: 'pageSize', description: '每页数量', required: false })
  async getServiceDurationRecords(
    @Query('orderId') orderId?: number,
    @Query('employeeId') employeeId?: number,
    @Query('serviceId') serviceId?: number,
    @Query('additionalServiceId') additionalServiceId?: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page') page: number = 1,
    @Query('pageSize') pageSize: number = 20
  ) {
    const where: any = {};

    if (orderId) {
      where.orderId = orderId;
    }

    if (employeeId) {
      where.employeeId = employeeId;
    }

    if (serviceId) {
      where.serviceId = serviceId;
    }

    if (additionalServiceId) {
      where.additionalServiceId = additionalServiceId;
    }

    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) {
        where.startTime[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        where.startTime[Op.lte] = new Date(endDate);
      }
    }

    const offset = (page - 1) * pageSize;

    const { rows: records, count } = await ServiceDurationRecord.findAndCountAll({
      where,
      include: [
        {
          model: Order,
          attributes: ['id', 'sn', 'status'],
          include: [
            {
              model: Customer,
              attributes: ['id', 'nickname', 'phone'],
            },
          ],
        },
        {
          model: OrderDetail,
          attributes: ['id', 'serviceName', 'petName'],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
        {
          model: Service,
          attributes: ['id', 'serviceName', 'avgDuration'],
        },
        {
          model: AdditionalService,
          attributes: ['id', 'name', 'duration'],
        },
      ],
      order: [['createdAt', 'DESC']],
      limit: pageSize,
      offset,
    });

    return {
      records,
      pagination: {
        total: count,
        page,
        pageSize,
        totalPages: Math.ceil(count / pageSize),
      },
    };
  }

  @Get('/service-statistics')
  @ApiOperation({ summary: '服务时长统计' })
  @ApiQuery({ name: 'serviceId', description: '服务ID', required: false })
  @ApiQuery({ name: 'startDate', description: '开始日期', required: false })
  @ApiQuery({ name: 'endDate', description: '结束日期', required: false })
  async getServiceStatistics(
    @Query('serviceId') serviceId?: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    const where: any = {
      recordType: 'main_service',
      duration: { [Op.not]: null },
    };

    if (serviceId) {
      where.serviceId = serviceId;
    }

    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) {
        where.startTime[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        where.startTime[Op.lte] = new Date(endDate);
      }
    }

    const records = await ServiceDurationRecord.findAll({
      where,
      include: [
        {
          model: Service,
          attributes: ['id', 'serviceName', 'avgDuration'],
        },
        {
          model: Employee,
          attributes: ['id', 'name'],
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    // 按服务分组统计
    const serviceStats = records.reduce((acc, record) => {
      const serviceId = record.serviceId;
      if (!acc[serviceId]) {
        acc[serviceId] = {
          serviceId,
          serviceName: record.service?.serviceName || record.serviceName,
          avgDuration: record.service?.avgDuration,
          totalRecords: 0,
          totalDuration: 0,
          minDuration: Number.MAX_SAFE_INTEGER,
          maxDuration: 0,
          durations: [],
        };
      }

      const duration = record.duration || 0;
      acc[serviceId].totalRecords++;
      acc[serviceId].totalDuration += duration;
      acc[serviceId].minDuration = Math.min(acc[serviceId].minDuration, duration);
      acc[serviceId].maxDuration = Math.max(acc[serviceId].maxDuration, duration);
      acc[serviceId].durations.push(duration);

      return acc;
    }, {});

    // 计算统计数据
    const statistics = Object.values(serviceStats).map((stat: any) => {
      const avgDuration = stat.totalRecords > 0 ? Math.round(stat.totalDuration / stat.totalRecords) : 0;
      
      return {
        serviceId: stat.serviceId,
        serviceName: stat.serviceName,
        systemAvgDuration: stat.avgDuration, // 系统记录的平均时长
        actualAvgDuration: avgDuration, // 实际计算的平均时长
        totalRecords: stat.totalRecords,
        totalDuration: stat.totalDuration,
        minDuration: stat.minDuration === Number.MAX_SAFE_INTEGER ? 0 : stat.minDuration,
        maxDuration: stat.maxDuration,
      };
    });

    return statistics;
  }

  @Get('/employee-statistics')
  @ApiOperation({ summary: '员工服务时长统计' })
  @ApiQuery({ name: 'employeeId', description: '员工ID', required: false })
  @ApiQuery({ name: 'startDate', description: '开始日期', required: false })
  @ApiQuery({ name: 'endDate', description: '结束日期', required: false })
  async getEmployeeStatistics(
    @Query('employeeId') employeeId?: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    const where: any = {
      duration: { [Op.not]: null },
    };

    if (employeeId) {
      where.employeeId = employeeId;
    }

    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) {
        where.startTime[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        where.startTime[Op.lte] = new Date(endDate);
      }
    }

    const records = await ServiceDurationRecord.findAll({
      where,
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    // 按员工分组统计
    const employeeStats = records.reduce((acc, record) => {
      const employeeId = record.employeeId;
      if (!acc[employeeId]) {
        acc[employeeId] = {
          employeeId,
          employeeName: record.employee?.name,
          employeePhone: record.employee?.phone,
          totalRecords: 0,
          totalDuration: 0,
          mainServiceRecords: 0,
          additionalServiceRecords: 0,
          mainServiceDuration: 0,
          additionalServiceDuration: 0,
        };
      }

      const duration = record.duration || 0;
      acc[employeeId].totalRecords++;
      acc[employeeId].totalDuration += duration;

      if (record.recordType === 'main_service') {
        acc[employeeId].mainServiceRecords++;
        acc[employeeId].mainServiceDuration += duration;
      } else {
        acc[employeeId].additionalServiceRecords++;
        acc[employeeId].additionalServiceDuration += duration;
      }

      return acc;
    }, {});

    // 计算统计数据
    const statistics = Object.values(employeeStats).map((stat: any) => {
      const avgDuration = stat.totalRecords > 0 ? Math.round(stat.totalDuration / stat.totalRecords) : 0;
      const avgMainServiceDuration = stat.mainServiceRecords > 0 ? Math.round(stat.mainServiceDuration / stat.mainServiceRecords) : 0;
      const avgAdditionalServiceDuration = stat.additionalServiceRecords > 0 ? Math.round(stat.additionalServiceDuration / stat.additionalServiceRecords) : 0;
      
      return {
        employeeId: stat.employeeId,
        employeeName: stat.employeeName,
        employeePhone: stat.employeePhone,
        totalRecords: stat.totalRecords,
        totalDuration: stat.totalDuration,
        avgDuration,
        mainServiceRecords: stat.mainServiceRecords,
        mainServiceDuration: stat.mainServiceDuration,
        avgMainServiceDuration,
        additionalServiceRecords: stat.additionalServiceRecords,
        additionalServiceDuration: stat.additionalServiceDuration,
        avgAdditionalServiceDuration,
      };
    });

    return statistics;
  }
}
